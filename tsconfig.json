{"compilerOptions": {"allowJs": false, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "jsx": "react-native", "module": "es2020", "moduleResolution": "node", "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "sourceMap": true, "target": "esnext", "lib": ["esnext", "dom"], "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "assets/*": ["./assets/*"]}, "typeRoots": ["./node_modules/@types", "./types"]}, "extends": "expo/tsconfig.base", "ts-node": {"compilerOptions": {"module": "commonjs"}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "src/utils/polyfills.ts"], "exclude": ["node_modules", "test/**/*"]}