import { View, TextInput, Text, TouchableOpacity, Modal, FlatList, Pressable } from "react-native"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { useAppTheme } from "@/utils/useAppTheme"
import { observer } from "mobx-react-lite"
import { useState } from "react"
import { $modalOverlay, $errorContainer, $errorText } from "@/styles/common"
import {
  $chatContainer,
  $chatTopContainer,
  $modelSelector,
  $modelPicker,
  $messageContainer,
  $roleText,
  $messageText,
  $bottomContainer,
  $pickerText,
  $pickerModal,
  $pickerOption,
  $pickerOptionText,
  $chatInput,
  $messagesScroll,
  $inputContainer,
  $sendButton,
  $sendButtonText,
  $newChatButton,
  $newChatButtonText,
} from "@/styles/chat"
import { KeyboardAvoidingView } from "react-native-keyboard-controller"
import { router, useLocalSearchParams } from "expo-router"
import { useMutation, useAction, useConvexAuth, useQuery } from "convex/react"
import { api } from "convex/_generated/api"
import { Message } from "./Message"
import { Id } from "convex/_generated/dataModel"
import { FREE_MODELS } from "@/services/models"

interface ChatInterfaceProps {
  apiEndpoint?: string
}

export const ChatInterface = observer(function ChatInterface({ apiEndpoint }: ChatInterfaceProps) {
  const { threadId } = useLocalSearchParams<{ threadId?: Id<"threads"> | undefined }>()
  const parsedThreadId = threadId ? threadId : undefined
  const [selectedModel, setSelectedModel] = useState(FREE_MODELS[0])
  const [isModelPickerVisible, setIsModelPickerVisible] = useState(false)

  const { theme, themed } = useAppTheme()
  const insets = useSafeAreaInsetsStyle(["bottom"])

  const [isSending, setIsSending] = useState(false)
  const [input, setInput] = useState("")
  const [error, setError] = useState<Error | null>(null)
  const { isAuthenticated } = useConvexAuth()

  const startChat = useAction(api.chat.startChatMessagePair)
  const createUserThread = useMutation(api.chat.createUserThread)

  const sendMessage = async () => {
    if (!input.trim()) return
    try {
      setIsSending(true)
      let currentThreadId = parsedThreadId
      if (!currentThreadId) {
        currentThreadId = await createUserThread({
          error: undefined,
        })
      }
      const { threadId } = await startChat({
        threadId: currentThreadId,
        content: input,
        model: selectedModel,
      })
      setInput("")
      setError(null)
      if (threadId && threadId !== parsedThreadId) {
        router.replace(`/${threadId}`)
      }
    } catch (err) {
      console.error(err)
      setError(err as Error)
    } finally {
      setIsSending(false)
    }
  }

  const handleSubmit = async () => {
    await sendMessage()
  }

  const messages = useQuery(
    api.messages.getMessages,
    parsedThreadId ? { threadId: parsedThreadId, limit: 100 } : "skip",
  )

  return (
    <KeyboardAvoidingView style={[themed($chatContainer), insets]}>
      <View style={themed($chatTopContainer)}>
        <View style={themed($modelSelector)}>
          <TouchableOpacity
            style={themed($modelPicker)}
            onPress={() => setIsModelPickerVisible(true)}
          >
            <Text style={themed($pickerText)}>{selectedModel}</Text>
          </TouchableOpacity>
          <Modal
            visible={isModelPickerVisible}
            transparent
            animationType="slide"
            onRequestClose={() => setIsModelPickerVisible(false)}
          >
            <TouchableOpacity
              style={themed($modalOverlay)}
              onPress={() => setIsModelPickerVisible(false)}
              activeOpacity={1}
            >
              <View style={themed($pickerModal)}>
                {FREE_MODELS.map((model) => (
                  <TouchableOpacity
                    key={model}
                    style={themed($pickerOption)}
                    onPress={() => {
                      setSelectedModel(model)
                      setIsModelPickerVisible(false)
                    }}
                  >
                    <Text style={themed($pickerOptionText)}>{model}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </TouchableOpacity>
          </Modal>
        </View>
        <FlatList
          style={themed($messagesScroll)}
          data={messages ?? []}
          keyExtractor={(message) => message._id}
          renderItem={({ item: message }) => (
            <Message
              key={message._id}
              role={message.role}
              content={message.content}
              isComplete={message.isComplete}
            />
          )}
          contentContainerStyle={{ flexGrow: 1, justifyContent: "flex-start" }}
          showsVerticalScrollIndicator={true}
        />
      </View>
      <View style={themed($bottomContainer)}>
        {error && (
          <View style={themed($errorContainer)}>
            <Text style={themed($errorText)}>{error.message}</Text>
          </View>
        )}
        <View style={themed($inputContainer)}>
          <TextInput
            style={[themed($chatInput), { flex: 1 }]}
            placeholder="Ask me anything..."
            value={input}
            onChangeText={setInput}
            onSubmitEditing={() => handleSubmit()}
            editable={!isSending}
            autoFocus={true}
          />
          <TouchableOpacity
            style={themed($sendButton)}
            onPress={() => handleSubmit()}
            disabled={!input.trim() || isSending}
          >
            <Text style={themed($sendButtonText)}>➤</Text>
          </TouchableOpacity>
        </View>
      </View>
    </KeyboardAvoidingView>
  )
})
