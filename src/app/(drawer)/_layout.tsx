import React, { useEffect, useState } from "react"
import { TouchableOpacity, Text, ActivityIndicator, View } from "react-native"
import { Drawer } from "expo-router/drawer"
import CustomDrawer from "@/components/CustomDrawer"
import { useRouter } from "expo-router"
import { useQuery } from "convex/react"
import { useConvexAuth } from "convex/react"
import { api } from "convex/_generated/api"
import { Redirect } from "expo-router"
import { useAppTheme } from "@/utils/useAppTheme"
import { $loadingContainer, $baseText } from "@/styles/common"

export default function ChatScreen() {
  const router = useRouter()
  const { themed } = useAppTheme()

  const { isAuthenticated, isLoading } = useConvexAuth()

  const [searchQuery, setSearchQuery] = useState("")

  // Always call hooks in the same order. Gate the query with "skip" when the user is
  // not authenticated so the hook call is still executed after sign-out.
  const filteredThreads = useQuery(
    api.chat.searchThreadsByTitle,
    isAuthenticated ? { searchQuery } : "skip",
  )

  // While loading, render nothing to avoid flicker.
  if (isLoading) {
    return (
      <View style={themed($loadingContainer)}>
        <ActivityIndicator size="large" color={themed((theme) => theme.colors.textDim)} />
        <Text style={themed($baseText)}>Loading...</Text>
      </View>
    )
  }

  useEffect((()))

  // Redirect unauthenticated users after hooks have been called.
  if (!isAuthenticated) {
    return <Redirect href={"/sign-in"} />
  }

  const handleLogin = () => {
    router.push("/sign-in")
  }

  return (
    <Drawer
      screenOptions={{
        headerShown: true,
      }}
      drawerContent={(props) => (
        <CustomDrawer
          {...props}
          chatThreads={filteredThreads}
          onLogin={handleLogin}
          onSearchChange={setSearchQuery}
          searchQuery={searchQuery}
        />
      )}
    >
      <Drawer.Screen
        name="index"
        options={{
          drawerLabel: "Create New Chat",
          title: "Create New Chat",
        }}
      />
      <Drawer.Screen
        name="[threadId]"
        options={{
          drawerLabel: "Chat Thread",
          title: "Chat Thread",
          headerRight: () => (
            <TouchableOpacity
              style={{
                backgroundColor: "#D97D54",
                borderRadius: 8,
                paddingHorizontal: 12,
                paddingVertical: 6,
                marginRight: 16,
              }}
              onPress={() => router.push("/")}
            >
              <Text style={{ color: "#fff", fontWeight: "600" }}>New Chat</Text>
            </TouchableOpacity>
          ),
        }}
      />
    </Drawer>
  )
}
