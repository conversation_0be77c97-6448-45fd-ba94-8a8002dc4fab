{"extends": "../tsconfig.json", "compilerOptions": {"allowJs": false, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "jsx": "react-native", "module": "es2015", "moduleResolution": "node", "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "sourceMap": true, "target": "esnext", "lib": ["esnext", "dom"], "skipLibCheck": true, "resolveJsonModule": true}, "include": ["**/*.test.ts", "**/*.test.tsx"]}